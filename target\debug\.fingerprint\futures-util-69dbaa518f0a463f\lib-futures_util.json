{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 5955513359176574756, "deps": [[5103565458935487, "futures_io", false, 1945299904991858235], [1615478164327904835, "pin_utils", false, 4261250437541294650], [1811549171721445101, "futures_channel", false, 11913410523260047313], [1906322745568073236, "pin_project_lite", false, 14143115448535745748], [5451793922601807560, "slab", false, 959222273327671818], [7013762810557009322, "futures_sink", false, 9889336387373861915], [7620660491849607393, "futures_core", false, 445487029194000766], [10565019901765856648, "futures_macro", false, 7893413755488833575], [15932120279885307830, "memchr", false, 12077273164913664859], [16240732885093539806, "futures_task", false, 6091372803729106252]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-69dbaa518f0a463f\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}