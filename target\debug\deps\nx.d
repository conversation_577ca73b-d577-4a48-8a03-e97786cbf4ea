D:\Sandeep\Anhything\New folder\target\debug\deps\nx.d: src\main.rs src\cli.rs src\types.rs src\utils.rs src\cache.rs src\resolver.rs src\installer.rs src\lockfile.rs src\ui.rs src\commands\mod.rs src\commands\install.rs src\commands\uninstall.rs src\commands\update.rs src\commands\list.rs src\commands\info.rs src\commands\audit.rs src\commands\run.rs src\commands\rebuild.rs src\commands\dedupe.rs src\commands\cache.rs src\commands\doctor.rs src\commands\benchmark.rs

D:\Sandeep\Anhything\New folder\target\debug\deps\nx.exe: src\main.rs src\cli.rs src\types.rs src\utils.rs src\cache.rs src\resolver.rs src\installer.rs src\lockfile.rs src\ui.rs src\commands\mod.rs src\commands\install.rs src\commands\uninstall.rs src\commands\update.rs src\commands\list.rs src\commands\info.rs src\commands\audit.rs src\commands\run.rs src\commands\rebuild.rs src\commands\dedupe.rs src\commands\cache.rs src\commands\doctor.rs src\commands\benchmark.rs

src\main.rs:
src\cli.rs:
src\types.rs:
src\utils.rs:
src\cache.rs:
src\resolver.rs:
src\installer.rs:
src\lockfile.rs:
src\ui.rs:
src\commands\mod.rs:
src\commands\install.rs:
src\commands\uninstall.rs:
src\commands\update.rs:
src\commands\list.rs:
src\commands\info.rs:
src\commands\audit.rs:
src\commands\run.rs:
src\commands\rebuild.rs:
src\commands\dedupe.rs:
src\commands\cache.rs:
src\commands\doctor.rs:
src\commands\benchmark.rs:

# env-dep:CARGO_PKG_VERSION=0.1.0
