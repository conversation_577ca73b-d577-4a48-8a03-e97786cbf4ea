{"rustc": 1842507548689473721, "features": "[\"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 5676177281124120482, "path": 5623080562496243947, "deps": [[1906322745568073236, "pin_project_lite", false, 13383973945353056484], [7620660491849607393, "futures_core", false, 17357246930372569157], [15932120279885307830, "memchr", false, 9308075938438998884], [17531218394775549125, "tokio", false, 15691876579898556638], [17772299992546037086, "flate2", false, 6766264418856031803]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\async-compression-1abdc4f3ba5266d7\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}