use anyhow::{Context, Result};
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;

use crate::cli::ListArgs;
use crate::types::PackageJson;
use crate::ui::UI;
use crate::utils::FileUtils;

pub async fn execute(args: ListArgs, ui: &UI) -> Result<()> {
    let current_dir = std::env::current_dir()
        .context("Failed to get current directory")?;

    let install_root = if args.global {
        FileUtils::get_global_path()?
    } else {
        current_dir
    };

    let node_modules = FileUtils::get_node_modules_path(&install_root);

    if !node_modules.exists() {
        ui.info("No packages installed");
        return Ok(());
    }

    if args.tree {
        list_tree(&node_modules, ui, args.depth).await
    } else {
        list_packages(&node_modules, ui, args.pattern.as_deref()).await
    }
}

async fn list_packages(node_modules: &Path, ui: &UI, pattern: Option<&str>) -> Result<()> {
    let mut packages = Vec::new();
    let mut entries = fs::read_dir(node_modules).await?;

    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        if path.is_dir() {
            let name = entry.file_name();
            let name_str = name.to_string_lossy();

            // Skip .bin and other special directories
            if name_str.starts_with('.') {
                continue;
            }

            // Handle scoped packages
            if name_str.starts_with('@') {
                let mut scoped_entries = fs::read_dir(&path).await?;
                while let Some(scoped_entry) = scoped_entries.next_entry().await? {
                    let scoped_path = scoped_entry.path();
                    if scoped_path.is_dir() {
                        let scoped_name = format!("{}/{}", name_str, scoped_entry.file_name().to_string_lossy());
                        if let Some(package_info) = get_package_info(&scoped_path).await? {
                            if pattern.map_or(true, |p| scoped_name.contains(p)) {
                                packages.push((scoped_name, package_info));
                            }
                        }
                    }
                }
            } else {
                if let Some(package_info) = get_package_info(&path).await? {
                    if pattern.map_or(true, |p| name_str.contains(p)) {
                        packages.push((name_str.to_string(), package_info));
                    }
                }
            }
        }
    }

    if packages.is_empty() {
        ui.info("No packages found");
        return Ok(());
    }

    // Sort packages by name
    packages.sort_by(|a, b| a.0.cmp(&b.0));

    // Display packages
    ui.info(&format!("Found {} packages:", packages.len()));
    println!("┌{:─<40}┬{:─<15}┬{:─<30}┐", "", "", "");
    println!("│{:^40}│{:^15}│{:^30}│", "Package", "Version", "Description");
    println!("├{:─<40}┼{:─<15}┼{:─<30}┤", "", "", "");

    for (name, info) in packages {
        let version = info.version.map(|v| v.to_string()).unwrap_or_else(|| "unknown".to_string());
        let description = info.description.unwrap_or_else(|| "".to_string());
        let truncated_desc = if description.len() > 27 {
            format!("{}...", &description[..24])
        } else {
            description
        };

        println!("│{:<40}│{:^15}│{:<30}│",
            if name.len() > 37 { format!("{}...", &name[..34]) } else { name },
            version,
            truncated_desc
        );
    }

    println!("└{:─<40}┴{:─<15}┴{:─<30}┘", "", "", "");

    Ok(())
}

async fn list_tree(node_modules: &Path, ui: &UI, max_depth: usize) -> Result<()> {
    ui.info("Dependency tree:");
    print_tree(node_modules, "", 0, max_depth).await?;
    Ok(())
}

fn print_tree<'a>(dir: &'a Path, prefix: &'a str, depth: usize, max_depth: usize) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<()>> + Send + 'a>> {
    Box::pin(async move {
    if depth >= max_depth {
        return Ok(());
    }

    let mut entries = fs::read_dir(dir).await?;
    let mut packages = Vec::new();

    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        if path.is_dir() {
            let name = entry.file_name();
            let name_str = name.to_string_lossy();

            if name_str.starts_with('.') {
                continue;
            }

            if name_str.starts_with('@') {
                let mut scoped_entries = fs::read_dir(&path).await?;
                while let Some(scoped_entry) = scoped_entries.next_entry().await? {
                    let scoped_path = scoped_entry.path();
                    if scoped_path.is_dir() {
                        let scoped_name = format!("{}/{}", name_str, scoped_entry.file_name().to_string_lossy());
                        packages.push((scoped_name, scoped_path));
                    }
                }
            } else {
                packages.push((name_str.to_string(), path));
            }
        }
    }

    packages.sort_by(|a, b| a.0.cmp(&b.0));

    for (i, (name, path)) in packages.iter().enumerate() {
        let is_last = i == packages.len() - 1;
        let connector = if is_last { "└── " } else { "├── " };

        if let Some(package_info) = get_package_info(path).await? {
            let version = package_info.version.map(|v| v.to_string()).unwrap_or_else(|| "unknown".to_string());
            println!("{}{}{} ({})", prefix, connector, name, version);

            let new_prefix = format!("{}{}", prefix, if is_last { "    " } else { "│   " });
            let deps_dir = path.join("node_modules");
            if deps_dir.exists() {
                print_tree(&deps_dir, &new_prefix, depth + 1, max_depth).await?;
            }
        }
    }

    Ok(())
    })
}

async fn get_package_info(package_path: &Path) -> Result<Option<PackageJson>> {
    let package_json_path = package_path.join("package.json");
    if package_json_path.exists() {
        match FileUtils::read_package_json(&package_json_path).await {
            Ok(package_json) => Ok(Some(package_json)),
            Err(_) => Ok(None),
        }
    } else {
        Ok(None)
    }
}
