{"rustc": 1842507548689473721, "features": "[\"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 15657897354478470176, "path": 5623080562496243947, "deps": [[1906322745568073236, "pin_project_lite", false, 14143115448535745748], [7620660491849607393, "futures_core", false, 445487029194000766], [15932120279885307830, "memchr", false, 12077273164913664859], [17531218394775549125, "tokio", false, 10799638968814880728], [17772299992546037086, "flate2", false, 1783241356417712011]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-da77961976be6d6f\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}