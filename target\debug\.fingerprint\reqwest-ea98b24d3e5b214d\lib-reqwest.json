{"rustc": 1842507548689473721, "features": "[\"__tls\", \"async-compression\", \"default\", \"default-tls\", \"gzip\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 5691985302134227101, "deps": [[40386456601120721, "percent_encoding", false, 3695463865203704889], [95042085696191081, "ipnet", false, 14389444837612251336], [264090853244900308, "sync_wrapper", false, 10125608333231193510], [784494742817713399, "tower_service", false, 2020720327814404280], [1288403060204016458, "tokio_util", false, 5758771037166478164], [1906322745568073236, "pin_project_lite", false, 14143115448535745748], [3150220818285335163, "url", false, 16766385760170489408], [3722963349756955755, "once_cell", false, 12205130416783021545], [4405182208873388884, "http", false, 14417772918648044992], [5986029879202738730, "log", false, 15840733446873181249], [7414427314941361239, "hyper", false, 6067253108794322529], [7620660491849607393, "futures_core", false, 445487029194000766], [8405603588346937335, "winreg", false, 2662328980027502297], [8915503303801890683, "http_body", false, 6767869072114582155], [9689903380558560274, "serde", false, 1624722604341103355], [10229185211513642314, "mime", false, 6563716855052569332], [10629569228670356391, "futures_util", false, 5831806631760967938], [12186126227181294540, "tokio_native_tls", false, 5348851528338150031], [12367227501898450486, "hyper_tls", false, 11340350532316814187], [13763625454224483636, "h2", false, 8654896843039994142], [14564311161534545801, "encoding_rs", false, 10586453415042180554], [14721851354164625169, "async_compression", false, 14624040940335849843], [16066129441945555748, "bytes", false, 13380718596227070736], [16311359161338405624, "rustls_pemfile", false, 8687677833978797491], [16362055519698394275, "serde_json", false, 12676874633449438599], [16542808166767769916, "serde_urlencoded", false, 8024620616735060705], [16785601910559813697, "native_tls_crate", false, 736701828952967146], [17531218394775549125, "tokio", false, 10799638968814880728], [18066890886671768183, "base64", false, 1896011070434129855]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-ea98b24d3e5b214d\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}