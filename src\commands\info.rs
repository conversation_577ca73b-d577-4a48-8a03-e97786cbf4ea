use anyhow::{Context, Result};
use std::sync::Arc;

use crate::cache::CacheManager;
use crate::cli::InfoArgs;
use crate::resolver::DependencyResolver;
use crate::ui::UI;
use crate::utils::UrlUtils;

pub async fn execute(args: InfoArgs, ui: &UI) -> Result<()> {
    let cache_manager = Arc::new(CacheManager::new()?);
    let registry_url = "https://registry.npmjs.org".to_string();
    let resolver = DependencyResolver::new(cache_manager, registry_url.clone());

    ui.info(&format!("Fetching information for {}...", args.package));

    // Check if package exists
    if !resolver.package_exists(&args.package).await {
        ui.error(&format!("Package '{}' not found", args.package));
        return Ok(());
    }

    // Get package metadata
    let version_req = if let Some(version) = args.version {
        semver::VersionReq::parse(&version)
            .with_context(|| format!("Invalid version: {}", version))?
    } else {
        semver::VersionReq::STAR
    };

    match resolver.resolve_single_package(&args.package, &version_req).await {
        Ok(metadata) => {
            display_package_info(&metadata, ui, args.detailed);
        }
        Err(e) => {
            ui.error(&format!("Failed to fetch package info: {}", e));
        }
    }

    Ok(())
}

fn display_package_info(metadata: &crate::types::PackageMetadata, ui: &UI, detailed: bool) {
    println!("\n{}", "Package Information".bold().underline());
    println!("┌{:─<30}┬{:─<50}┐", "", "");
    println!("│{:^30}│{:^50}│", "Field", "Value");
    println!("├{:─<30}┼{:─<50}┤", "", "");

    println!("│{:<30}│{:<50}│", "Name", metadata.name);
    println!("│{:<30}│{:<50}│", "Version", metadata.version.to_string());

    if let Some(description) = &metadata.description {
        let truncated = if description.len() > 47 {
            format!("{}...", &description[..44])
        } else {
            description.clone()
        };
        println!("│{:<30}│{:<50}│", "Description", truncated);
    }

    if let Some(license) = &metadata.license {
        println!("│{:<30}│{:<50}│", "License", license);
    }

    if let Some(homepage) = &metadata.homepage {
        let truncated = if homepage.len() > 47 {
            format!("{}...", &homepage[..44])
        } else {
            homepage.clone()
        };
        println!("│{:<30}│{:<50}│", "Homepage", truncated);
    }

    if let Some(author) = &metadata.author {
        println!("│{:<30}│{:<50}│", "Author", author.name);
    }

    println!("│{:<30}│{:<50}│", "Dependencies", metadata.dependencies.len().to_string());
    println!("│{:<30}│{:<50}│", "Dev Dependencies", metadata.dev_dependencies.len().to_string());

    if let Some(size) = metadata.dist.unpack_size {
        println!("│{:<30}│{:<50}│", "Unpacked Size", format_size(size));
    }

    println!("│{:<30}│{:<50}│", "Published", metadata.published_at.format("%Y-%m-%d").to_string());

    if let Some(deprecated) = &metadata.deprecated {
        println!("│{:<30}│{:<50}│", "⚠️ DEPRECATED", "Yes");
        let truncated = if deprecated.len() > 47 {
            format!("{}...", &deprecated[..44])
        } else {
            deprecated.clone()
        };
        println!("│{:<30}│{:<50}│", "Deprecation Notice", truncated);
    }

    println!("└{:─<30}┴{:─<50}┘", "", "");

    if detailed {
        display_detailed_info(metadata, ui);
    }
}

fn display_detailed_info(metadata: &crate::types::PackageMetadata, _ui: &UI) {
    if !metadata.keywords.is_empty() {
        println!("\n{}", "Keywords".bold());
        println!("{}", metadata.keywords.join(", "));
    }

    if !metadata.dependencies.is_empty() {
        println!("\n{}", "Dependencies".bold());
        for (name, version) in &metadata.dependencies {
            println!("  {} {}", name, version);
        }
    }

    if !metadata.dev_dependencies.is_empty() {
        println!("\n{}", "Dev Dependencies".bold());
        for (name, version) in &metadata.dev_dependencies {
            println!("  {} {}", name, version);
        }
    }

    if !metadata.peer_dependencies.is_empty() {
        println!("\n{}", "Peer Dependencies".bold());
        for (name, version) in &metadata.peer_dependencies {
            println!("  {} {}", name, version);
        }
    }

    if !metadata.optional_dependencies.is_empty() {
        println!("\n{}", "Optional Dependencies".bold());
        for (name, version) in &metadata.optional_dependencies {
            println!("  {} {}", name, version);
        }
    }

    if let Some(bin) = &metadata.bin {
        if !bin.is_empty() {
            println!("\n{}", "Binaries".bold());
            for (name, path) in bin {
                println!("  {} -> {}", name, path);
            }
        }
    }

    if !metadata.scripts.is_empty() {
        println!("\n{}", "Scripts".bold());
        for (name, script) in &metadata.scripts {
            println!("  {}: {}", name, script);
        }
    }

    if !metadata.engines.is_empty() {
        println!("\n{}", "Engines".bold());
        for (engine, version) in &metadata.engines {
            println!("  {}: {}", engine, version);
        }
    }

    if let Some(repository) = &metadata.repository {
        println!("\n{}", "Repository".bold());
        println!("  Type: {}", repository.repo_type);
        println!("  URL: {}", repository.url);
        if let Some(directory) = &repository.directory {
            println!("  Directory: {}", directory);
        }
    }

    if !metadata.maintainers.is_empty() {
        println!("\n{}", "Maintainers".bold());
        for maintainer in &metadata.maintainers {
            print!("  {}", maintainer.name);
            if let Some(email) = &maintainer.email {
                print!(" <{}>", email);
            }
            println!();
        }
    }
}

fn format_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

// Add missing imports
use colored::*;
