#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🚀 Setting up nx package manager...');

const platform = os.platform();
const arch = os.arch() === 'x64' ? 'x64' : 'arm64';

// Determine the correct binary for this platform
const binDir = path.join(__dirname, '..', 'bin');
const platformBinary = path.join(binDir, `nx-${platform}-${arch}${platform === 'win32' ? '.exe' : ''}`);
const genericBinary = path.join(binDir, 'nx');

let binaryToUse = null;

// Check for platform-specific binary first
if (fs.existsSync(platformBinary)) {
  binaryToUse = platformBinary;
  console.log(`✅ Found platform-specific binary: ${path.basename(platformBinary)}`);
} else if (fs.existsSync(genericBinary)) {
  binaryToUse = genericBinary;
  console.log(`✅ Using generic binary: ${path.basename(genericBinary)}`);
} else {
  console.error('❌ No compatible binary found for your platform');
  console.error(`Platform: ${platform}, Architecture: ${arch}`);
  console.error('Available binaries:');
  
  if (fs.existsSync(binDir)) {
    const files = fs.readdirSync(binDir);
    files.forEach(file => console.error(`  - ${file}`));
  } else {
    console.error('  (bin directory not found)');
  }
  
  process.exit(1);
}

// Create symlink or copy for the main nx binary
const mainBinary = path.join(binDir, `nx${platform === 'win32' ? '.exe' : ''}`);

try {
  // Remove existing binary if it exists
  if (fs.existsSync(mainBinary)) {
    fs.unlinkSync(mainBinary);
  }
  
  // Copy the appropriate binary
  fs.copyFileSync(binaryToUse, mainBinary);
  
  // Make executable on Unix systems
  if (platform !== 'win32') {
    fs.chmodSync(mainBinary, '755');
  }
  
  console.log(`✅ nx binary ready at: ${mainBinary}`);
} catch (error) {
  console.error('❌ Failed to set up nx binary:', error.message);
  process.exit(1);
}

// Verify the binary works
try {
  const { execSync } = require('child_process');
  const output = execSync(`"${mainBinary}" --help`, { encoding: 'utf8', timeout: 5000 });
  
  if (output.includes('Ultra-fast package manager')) {
    console.log('✅ nx installation verified successfully');
    console.log('🎉 You can now use nx as your package manager!');
    console.log('');
    console.log('Quick start:');
    console.log('  nx install express    # Install a package');
    console.log('  nx list              # List installed packages');
    console.log('  nx run start         # Run a script');
    console.log('  nx --help            # Show all commands');
  } else {
    throw new Error('Unexpected output from nx binary');
  }
} catch (error) {
  console.warn('⚠️ Could not verify nx installation:', error.message);
  console.warn('The binary was installed but may not work correctly');
}

// Check for Node.js version compatibility
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.warn('⚠️ Warning: nx requires Node.js 16 or higher');
  console.warn(`Current version: ${nodeVersion}`);
  console.warn('Some features may not work correctly');
}

console.log('📦 Installation complete!');
