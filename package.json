{"name": "nx-pm", "version": "0.1.0", "description": "Ultra-fast package manager for Node.js ecosystems", "main": "index.js", "bin": {"nx": "./bin/nx"}, "scripts": {"build": "cargo build --release", "build-all": "node scripts/build-all.js", "prepare": "node scripts/prepare.js", "postinstall": "node scripts/postinstall.js", "test": "cargo test", "clean": "cargo clean && rimraf bin/ dist/"}, "keywords": ["package-manager", "npm", "node", "cli", "fast", "rust", "performance"], "author": "NX Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nx-pm/nx.git"}, "homepage": "https://nx-pm.dev", "bugs": {"url": "https://github.com/nx-pm/nx/issues"}, "engines": {"node": ">=16.0.0"}, "os": ["win32", "darwin", "linux"], "cpu": ["x64", "arm64"], "files": ["bin/", "scripts/", "README.md", "LICENSE"], "devDependencies": {"rimraf": "^5.0.0"}, "optionalDependencies": {}, "funding": {"type": "github", "url": "https://github.com/sponsors/nx-pm"}}