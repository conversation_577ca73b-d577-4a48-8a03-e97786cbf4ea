use assert_cmd::Command;
use predicates::prelude::*;
use std::fs;
use tempfile::TempDir;

#[test]
fn test_nx_help() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.arg("--help");
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Ultra-fast package manager"));
}

#[test]
fn test_nx_doctor() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.arg("doctor");
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Doctor command not yet implemented"));
}

#[test]
fn test_nx_list_no_packages() {
    let temp_dir = TempDir::new().unwrap();
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.current_dir(temp_dir.path());
    cmd.arg("list");
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("No packages installed"));
}

#[test]
fn test_nx_list_help() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["list", "--help"]);
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("List installed packages"));
}

#[test]
fn test_nx_info_help() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["info", "--help"]);
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Show package information"));
}

#[test]
fn test_nx_run_help() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["run", "--help"]);
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Run package scripts"));
}

#[test]
fn test_nx_install_help() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["install", "--help"]);
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Install packages"));
}

#[test]
fn test_nx_uninstall_help() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["uninstall", "--help"]);
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Uninstall packages"));
}

#[test]
fn test_nx_update_help() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["update", "--help"]);
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Update packages"));
}

#[test]
fn test_nx_run_list_scripts_no_package_json() {
    let temp_dir = TempDir::new().unwrap();
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.current_dir(temp_dir.path());
    cmd.args(&["run", "test", "--list"]);
    cmd.assert()
        .success()
        .stderr(predicate::str::contains("No package.json found"));
}

#[test]
fn test_nx_run_list_scripts_with_package_json() {
    let temp_dir = TempDir::new().unwrap();
    
    // Create a package.json with scripts
    let package_json = r#"{
  "name": "test-package",
  "version": "1.0.0",
  "scripts": {
    "test": "echo 'Running tests'",
    "build": "echo 'Building project'",
    "start": "echo 'Starting server'"
  }
}"#;
    
    fs::write(temp_dir.path().join("package.json"), package_json).unwrap();
    
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.current_dir(temp_dir.path());
    cmd.args(&["run", "nonexistent", "--list"]);
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Available scripts"));
}

#[test]
fn test_nx_install_no_packages() {
    let temp_dir = TempDir::new().unwrap();
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.current_dir(temp_dir.path());
    cmd.arg("install");
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("No packages to install"));
}

#[test]
fn test_nx_uninstall_no_packages() {
    let temp_dir = TempDir::new().unwrap();
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.current_dir(temp_dir.path());
    cmd.arg("uninstall");
    cmd.assert()
        .success()
        .stderr(predicate::str::contains("No packages specified"));
}

#[test]
fn test_nx_emoji_disabled() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["--no-emoji", "doctor"]);
    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Doctor command not yet implemented"))
        .stdout(predicate::str::contains("ℹ️").not());
}

#[test]
fn test_nx_verbose_mode() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["--verbose", "doctor"]);
    cmd.assert()
        .success();
}

#[test]
fn test_nx_theme_light() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["--theme", "light", "doctor"]);
    cmd.assert()
        .success();
}

#[test]
fn test_nx_dry_run() {
    let mut cmd = Command::cargo_bin("nx").unwrap();
    cmd.args(&["--dry-run", "doctor"]);
    cmd.assert()
        .success();
}
