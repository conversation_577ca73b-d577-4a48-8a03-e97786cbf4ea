use anyhow::Result;
use clap::Parser;
use colored::*;
use std::env;
use tracing::{error, info};
use tracing_subscriber::{fmt, EnvFilter};

mod cli;
mod types;
mod utils;
mod cache;
mod resolver;
mod installer;
mod lockfile;
mod ui;
mod commands;

use cli::{Cli, Commands};
use ui::UI;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    let filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));
    
    fmt()
        .with_env_filter(filter)
        .with_target(false)
        .with_level(false)
        .init();

    // Parse command line arguments
    let cli = Cli::parse();
    
    // Initialize UI
    let ui = UI::new(cli.verbose, cli.no_emoji, cli.theme.clone());
    
    // Execute the command
    let result = match cli.command {
        Commands::Install(args) => commands::install::execute(args, &ui).await,
        Commands::Uninstall(args) => commands::uninstall::execute(args, &ui).await,
        Commands::Update(args) => commands::update::execute(args, &ui).await,
        Commands::List(args) => commands::list::execute(args, &ui).await,
        Commands::Info(args) => commands::info::execute(args, &ui).await,
        Commands::Audit(args) => commands::audit::execute(args, &ui).await,
        Commands::Run(args) => commands::run::execute(args, &ui).await,
        Commands::Exec(args) => commands::run::execute_binary(args, &ui).await,
        Commands::Ci(args) => commands::install::execute_ci(args, &ui).await,
        Commands::Rebuild(args) => commands::rebuild::execute(args, &ui).await,
        Commands::Dedupe(args) => commands::dedupe::execute(args, &ui).await,
        Commands::Cache(args) => commands::cache::execute(args, &ui).await,
        Commands::Doctor => commands::doctor::execute(&ui).await,
        Commands::Benchmark(args) => commands::benchmark::execute(args, &ui).await,
    };

    match result {
        Ok(_) => {
            info!("Command completed successfully");
            Ok(())
        }
        Err(e) => {
            ui.error(&format!("Error: {}", e));
            error!("Command failed: {}", e);
            std::process::exit(1);
        }
    }
}
