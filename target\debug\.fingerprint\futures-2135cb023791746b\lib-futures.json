{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 902712116728367258, "deps": [[5103565458935487, "futures_io", false, 1945299904991858235], [1811549171721445101, "futures_channel", false, 11913410523260047313], [7013762810557009322, "futures_sink", false, 9889336387373861915], [7620660491849607393, "futures_core", false, 445487029194000766], [10629569228670356391, "futures_util", false, 5831806631760967938], [12779779637805422465, "futures_executor", false, 13563844191352655205], [16240732885093539806, "futures_task", false, 6091372803729106252]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-2135cb023791746b\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}