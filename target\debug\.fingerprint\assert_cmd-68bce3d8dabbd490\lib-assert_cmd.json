{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"color\", \"color-auto\"]", "target": 16602908775176716730, "profile": 13766548740317204221, "path": 14197734989420796732, "deps": [[904119603456001782, "bstr", false, 10018471336288544622], [6491540798839599208, "predicates_core", false, 13058486803298169727], [9394696648929125047, "anstyle", false, 9443881581786930913], [12516616738327129663, "predicates_tree", false, 2440962417054233563], [12939671402123591185, "build_script_build", false, 5401483083098740460], [15863765456528386755, "predicates", false, 11228318872561984064], [17492147245553934378, "wait_timeout", false, 1544241332289343007], [18000218614148971598, "doc_comment", false, 2280418158216534619]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\assert_cmd-68bce3d8dabbd490\\dep-lib-assert_cmd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}