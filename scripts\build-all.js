#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Platform-specific build configurations
const targets = [
  { platform: 'win32', arch: 'x64', target: 'x86_64-pc-windows-msvc', ext: '.exe' },
  { platform: 'win32', arch: 'arm64', target: 'aarch64-pc-windows-msvc', ext: '.exe' },
  { platform: 'darwin', arch: 'x64', target: 'x86_64-apple-darwin', ext: '' },
  { platform: 'darwin', arch: 'arm64', target: 'aarch64-apple-darwin', ext: '' },
  { platform: 'linux', arch: 'x64', target: 'x86_64-unknown-linux-gnu', ext: '' },
  { platform: 'linux', arch: 'arm64', target: 'aarch64-unknown-linux-gnu', ext: '' },
];

const currentPlatform = os.platform();
const currentArch = os.arch() === 'x64' ? 'x64' : 'arm64';

console.log('🔨 Building nx for all platforms...');

// Create bin directory if it doesn't exist
const binDir = path.join(__dirname, '..', 'bin');
if (!fs.existsSync(binDir)) {
  fs.mkdirSync(binDir, { recursive: true });
}

// Build for current platform first (for testing)
const currentTarget = targets.find(t => t.platform === currentPlatform && t.arch === currentArch);
if (currentTarget) {
  console.log(`📦 Building for current platform: ${currentTarget.platform}-${currentTarget.arch}`);
  try {
    execSync(`cargo build --release --target ${currentTarget.target}`, { stdio: 'inherit' });
    
    const sourcePath = path.join(__dirname, '..', 'target', currentTarget.target, 'release', `nx${currentTarget.ext}`);
    const destPath = path.join(binDir, `nx-${currentTarget.platform}-${currentTarget.arch}${currentTarget.ext}`);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✅ Built: ${destPath}`);
    }
  } catch (error) {
    console.error(`❌ Failed to build for ${currentTarget.platform}-${currentTarget.arch}:`, error.message);
  }
}

// Build for other platforms (cross-compilation)
for (const target of targets) {
  if (target.platform === currentPlatform && target.arch === currentArch) {
    continue; // Already built above
  }
  
  console.log(`📦 Cross-compiling for: ${target.platform}-${target.arch}`);
  
  try {
    // Add target if not already added
    try {
      execSync(`rustup target add ${target.target}`, { stdio: 'pipe' });
    } catch (e) {
      // Target might already be added
    }
    
    // Cross-compile
    execSync(`cargo build --release --target ${target.target}`, { stdio: 'inherit' });
    
    const sourcePath = path.join(__dirname, '..', 'target', target.target, 'release', `nx${target.ext}`);
    const destPath = path.join(binDir, `nx-${target.platform}-${target.arch}${target.ext}`);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✅ Built: ${destPath}`);
    } else {
      console.warn(`⚠️ Binary not found: ${sourcePath}`);
    }
  } catch (error) {
    console.warn(`⚠️ Cross-compilation failed for ${target.platform}-${target.arch}:`, error.message);
    console.warn('This is normal if cross-compilation toolchain is not set up');
  }
}

console.log('🎉 Build process completed!');
console.log('📁 Binaries are available in the bin/ directory');

// List built binaries
const builtBinaries = fs.readdirSync(binDir).filter(file => file.startsWith('nx-'));
console.log('\n📋 Built binaries:');
builtBinaries.forEach(binary => {
  const stats = fs.statSync(path.join(binDir, binary));
  const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
  console.log(`  ${binary} (${sizeMB} MB)`);
});
