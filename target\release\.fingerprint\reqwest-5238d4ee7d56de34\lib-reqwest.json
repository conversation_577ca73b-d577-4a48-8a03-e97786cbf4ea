{"rustc": 1842507548689473721, "features": "[\"__tls\", \"async-compression\", \"default\", \"default-tls\", \"gzip\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5676177281124120482, "path": 5691985302134227101, "deps": [[40386456601120721, "percent_encoding", false, 7027504397992519091], [95042085696191081, "ipnet", false, 7074937951422421799], [264090853244900308, "sync_wrapper", false, 16287300230532063256], [784494742817713399, "tower_service", false, 14436635265337421321], [1288403060204016458, "tokio_util", false, 3145846850509305781], [1906322745568073236, "pin_project_lite", false, 13383973945353056484], [3150220818285335163, "url", false, 13307466015446296241], [3722963349756955755, "once_cell", false, 7829772604897079390], [4405182208873388884, "http", false, 7915962731490741724], [5986029879202738730, "log", false, 11337672393928024211], [7414427314941361239, "hyper", false, 2332651003055634877], [7620660491849607393, "futures_core", false, 17357246930372569157], [8405603588346937335, "winreg", false, 16396730239370082046], [8915503303801890683, "http_body", false, 14954643760364509123], [9689903380558560274, "serde", false, 6839975458898293775], [10229185211513642314, "mime", false, 3962299585748451543], [10629569228670356391, "futures_util", false, 11166704376373411730], [12186126227181294540, "tokio_native_tls", false, 10492019095501936087], [12367227501898450486, "hyper_tls", false, 17393669569189484884], [13763625454224483636, "h2", false, 2036052319992670543], [14564311161534545801, "encoding_rs", false, 3792282802946354693], [14721851354164625169, "async_compression", false, 3469527469598069728], [16066129441945555748, "bytes", false, 9448655984143331846], [16311359161338405624, "rustls_pemfile", false, 8021952664347316635], [16362055519698394275, "serde_json", false, 5652173684655352898], [16542808166767769916, "serde_urlencoded", false, 5753863381794168064], [16785601910559813697, "native_tls_crate", false, 2618336512919022541], [17531218394775549125, "tokio", false, 15691876579898556638], [18066890886671768183, "base64", false, 5083577029454862003]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-5238d4ee7d56de34\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}