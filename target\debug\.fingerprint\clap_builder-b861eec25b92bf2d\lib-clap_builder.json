{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15221872889701672926, "path": 4399355794312995255, "deps": [[5820056977320921005, "anstream", false, 6681219727795421706], [9394696648929125047, "anstyle", false, 9443881581786930913], [11166530783118767604, "strsim", false, 832594368702808904], [11649982696571033535, "clap_lex", false, 16410580787594082858]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-b861eec25b92bf2d\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}