{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 10812796613434319891, "deps": [[1009387600818341822, "matchers", false, 1730642966846366401], [1017461770342116999, "sharded_slab", false, 12117678979944841731], [1359731229228270592, "thread_local", false, 13848253013376960328], [3424551429995674438, "tracing_core", false, 2526329775277041311], [3666196340704888985, "smallvec", false, 12220969047952470426], [3722963349756955755, "once_cell", false, 12205130416783021545], [8606274917505247608, "tracing", false, 1466104831015244939], [8614575489689151157, "nu_ansi_term", false, 1886787916475149847], [9451456094439810778, "regex", false, 16125051047567819461], [10806489435541507125, "tracing_log", false, 6980856592829588546]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-3b78a6230be976b4\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}