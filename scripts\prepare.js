#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🔧 Preparing nx package for publication...');

// Ensure Rust is available
try {
  execSync('cargo --version', { stdio: 'pipe' });
  console.log('✅ Rust toolchain detected');
} catch (error) {
  console.error('❌ Rust toolchain not found. Please install Rust from https://rustup.rs/');
  process.exit(1);
}

// Build for current platform
const platform = os.platform();
const arch = os.arch() === 'x64' ? 'x64' : 'arm64';

console.log(`📦 Building nx for ${platform}-${arch}...`);

try {
  execSync('cargo build --release', { stdio: 'inherit' });
  console.log('✅ Build completed successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Copy binary to bin directory
const binDir = path.join(__dirname, '..', 'bin');
if (!fs.existsSync(binDir)) {
  fs.mkdirSync(binDir, { recursive: true });
}

const ext = platform === 'win32' ? '.exe' : '';
const sourcePath = path.join(__dirname, '..', 'target', 'release', `nx${ext}`);
const destPath = path.join(binDir, 'nx');

if (fs.existsSync(sourcePath)) {
  fs.copyFileSync(sourcePath, destPath);
  
  // Make executable on Unix systems
  if (platform !== 'win32') {
    fs.chmodSync(destPath, '755');
  }
  
  console.log(`✅ Binary copied to: ${destPath}`);
  
  // Check binary size
  const stats = fs.statSync(destPath);
  const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
  console.log(`📏 Binary size: ${sizeMB} MB`);
  
  if (stats.size > 15 * 1024 * 1024) {
    console.warn('⚠️ Binary size exceeds 15MB target');
  }
} else {
  console.error(`❌ Binary not found at: ${sourcePath}`);
  process.exit(1);
}

console.log('🎉 Package preparation completed!');
console.log('📦 Ready for npm publish');
