{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"default\", \"export-internal\", \"grammar-extras\", \"not-bootstrap-in-src\", \"std\"]", "target": 3031267579843285925, "profile": 2225463790103693989, "path": 14602164449950254386, "deps": [[3060637413840920116, "proc_macro2", false, 10474681983785299416], [3221585212778410572, "pest", false, 4200719060958297665], [3395339557636834855, "pest_meta", false, 4899641029643782871], [4974441333307933176, "syn", false, 5646645333714428030], [17990358020177143287, "quote", false, 2558279361670374754]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pest_generator-c3ed1533c7c6ee60\\dep-lib-pest_generator", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}