use clap::{Parser, Subcommand, Args};

#[derive(Parser)]
#[command(name = "nx")]
#[command(about = "Ultra-fast package manager for Node.js ecosystems")]
#[command(version = env!("CARGO_PKG_VERSION"))]
#[command(author = "NX Team")]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,

    /// Enable verbose output
    #[arg(short, long, global = true)]
    pub verbose: bool,

    /// Disable emoji output
    #[arg(long, global = true)]
    pub no_emoji: bool,

    /// Set UI theme (dark, light)
    #[arg(long, global = true, default_value = "dark")]
    pub theme: String,



    /// Custom registry URL
    #[arg(long, global = true)]
    pub registry: Option<String>,

    /// Log output to file
    #[arg(long, global = true)]
    pub log_file: Option<String>,

    /// Dry run mode (simulate operations)
    #[arg(long, global = true)]
    pub dry_run: bool,

    /// Output in JSON format
    #[arg(long, global = true)]
    pub json: bool,
}

#[derive(Subcommand)]
pub enum Commands {
    /// Install packages
    #[command(alias = "i")]
    Install(InstallArgs),

    /// Uninstall packages
    #[command(alias = "remove", alias = "rm")]
    Uninstall(UninstallArgs),

    /// Update packages
    #[command(alias = "upgrade")]
    Update(UpdateArgs),

    /// List installed packages
    #[command(alias = "ls")]
    List(ListArgs),

    /// Show package information
    Info(InfoArgs),

    /// Run security audit
    Audit(AuditArgs),

    /// Run package scripts
    Run(RunArgs),

    /// Execute package binaries
    Exec(ExecArgs),

    /// Clean install from lockfile
    Ci(CiArgs),

    /// Rebuild native modules
    Rebuild(RebuildArgs),

    /// Remove duplicate dependencies
    Dedupe(DedupeArgs),

    /// Manage cache
    Cache(CacheArgs),

    /// Diagnose issues
    Doctor,

    /// Benchmark against other package managers
    Benchmark(BenchmarkArgs),
}

#[derive(Args)]
pub struct InstallArgs {
    /// Package names to install
    pub packages: Vec<String>,

    /// Install globally
    #[arg(short, long)]
    pub global: bool,

    /// Save as dev dependency
    #[arg(short = 'D', long)]
    pub save_dev: bool,

    /// Save as optional dependency
    #[arg(short = 'O', long)]
    pub save_optional: bool,

    /// Save as peer dependency
    #[arg(short = 'P', long)]
    pub save_peer: bool,

    /// Don't save to package.json
    #[arg(long)]
    pub no_save: bool,

    /// Force reinstall
    #[arg(short, long)]
    pub force: bool,

    /// Skip optional dependencies
    #[arg(long)]
    pub no_optional: bool,

    /// Production install only
    #[arg(long)]
    pub production: bool,

    /// Interactive mode for version selection
    #[arg(short, long)]
    pub interactive: bool,
}

#[derive(Args)]
pub struct UninstallArgs {
    /// Package names to uninstall
    pub packages: Vec<String>,

    /// Uninstall globally
    #[arg(short, long)]
    pub global: bool,

    /// Don't save changes to package.json
    #[arg(long)]
    pub no_save: bool,
}

#[derive(Args)]
pub struct UpdateArgs {
    /// Package names to update (empty for all)
    pub packages: Vec<String>,

    /// Update globally installed packages
    #[arg(short, long)]
    pub global: bool,

    /// Show outdated packages without updating
    #[arg(long)]
    pub outdated: bool,

    /// Interactive mode for version selection
    #[arg(short, long)]
    pub interactive: bool,
}

#[derive(Args)]
pub struct ListArgs {
    /// List globally installed packages
    #[arg(short, long)]
    pub global: bool,

    /// Show dependency tree
    #[arg(long)]
    pub tree: bool,

    /// Show outdated packages
    #[arg(long)]
    pub outdated: bool,

    /// Filter by package name pattern
    #[arg(long)]
    pub pattern: Option<String>,

    /// Maximum depth for tree view
    #[arg(long, default_value = "3")]
    pub depth: usize,
}

#[derive(Args)]
pub struct InfoArgs {
    /// Package name
    pub package: String,

    /// Show specific version
    #[arg(long)]
    pub version: Option<String>,

    /// Show detailed information
    #[arg(long)]
    pub detailed: bool,
}

#[derive(Args)]
pub struct AuditArgs {
    /// Automatically fix vulnerabilities
    #[arg(long)]
    pub fix: bool,

    /// Force audit even with cache
    #[arg(long)]
    pub force: bool,

    /// Show only specific severity levels
    #[arg(long)]
    pub level: Option<String>,
}

#[derive(Args)]
pub struct RunArgs {
    /// Script name to run
    pub script: String,

    /// Arguments to pass to the script
    pub args: Vec<String>,

    /// Show available scripts
    #[arg(long)]
    pub list: bool,
}

#[derive(Args)]
pub struct ExecArgs {
    /// Binary name to execute
    pub binary: String,

    /// Arguments to pass to the binary
    pub args: Vec<String>,
}

#[derive(Args)]
pub struct CiArgs {
    /// Force clean install
    #[arg(long)]
    pub force: bool,

    /// Skip optional dependencies
    #[arg(long)]
    pub no_optional: bool,

    /// Production install only
    #[arg(long)]
    pub production: bool,
}

#[derive(Args)]
pub struct RebuildArgs {
    /// Specific packages to rebuild
    pub packages: Vec<String>,

    /// Force rebuild all native modules
    #[arg(long)]
    pub force: bool,
}

#[derive(Args)]
pub struct DedupeArgs {
    /// Dry run to show what would be deduplicated
    #[arg(long)]
    pub dry_run: bool,
}

#[derive(Args)]
pub struct CacheArgs {
    #[command(subcommand)]
    pub command: CacheCommands,
}

#[derive(Subcommand)]
pub enum CacheCommands {
    /// Clear cache
    Clear,
    /// Show cache statistics
    Stats,
    /// Verify cache integrity
    Verify,
}

#[derive(Args)]
pub struct BenchmarkArgs {
    /// Package managers to compare against
    #[arg(long, default_values = &["npm", "yarn", "pnpm", "bun"])]
    pub managers: Vec<String>,

    /// Test project directory
    #[arg(long)]
    pub project: Option<String>,

    /// Number of benchmark runs
    #[arg(long, default_value = "3")]
    pub runs: usize,
}
