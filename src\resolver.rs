use anyhow::{Context, Result};
use semver::{Version, VersionReq};
use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use tokio::sync::Semaphore;

use crate::cache::CacheManager;
use crate::types::{PackageMetadata, ResolvedDependency, ResolutionContext, NxError};
use crate::utils::{HttpClient, UrlUtils};

/// Dependency resolver with conflict resolution and circular dependency detection
pub struct DependencyResolver {
    http_client: HttpClient,
    cache_manager: Arc<CacheManager>,
    registry_url: String,
    max_concurrent_requests: usize,
}

impl DependencyResolver {
    pub fn new(cache_manager: Arc<CacheManager>, registry_url: String) -> Self {
        Self {
            http_client: HttpClient::new(),
            cache_manager,
            registry_url,
            max_concurrent_requests: 50,
        }
    }

    /// Resolve dependency tree for given packages
    pub async fn resolve_dependencies(
        &self,
        packages: &[(String, VersionReq)],
        context: &ResolutionContext,
    ) -> Result<HashMap<String, ResolvedDependency>> {
        let mut resolved = HashMap::new();
        let mut resolution_queue = VecDeque::new();
        let mut visited = HashSet::new();
        let mut resolving = HashSet::new();
        
        // Add initial packages to queue
        for (name, version_req) in packages {
            resolution_queue.push_back((name.clone(), version_req.clone(), false, false, false));
        }

        let semaphore = Arc::new(Semaphore::new(self.max_concurrent_requests));

        while let Some((package_name, version_req, is_dev, is_optional, is_peer)) = resolution_queue.pop_front() {
            let key = format!("{}@{}", package_name, version_req);
            
            if visited.contains(&key) {
                continue;
            }

            // Check for circular dependencies
            if resolving.contains(&package_name) {
                eprintln!("Warning: Circular dependency detected for {}", package_name);
                continue;
            }

            resolving.insert(package_name.clone());
            visited.insert(key.clone());

            // Skip dev dependencies in production mode
            if context.production_only && is_dev {
                resolving.remove(&package_name);
                continue;
            }

            // Skip optional dependencies if not included
            if !context.include_optional && is_optional {
                resolving.remove(&package_name);
                continue;
            }

            // Skip peer dependencies if not included
            if !context.include_peer && is_peer {
                resolving.remove(&package_name);
                continue;
            }

            let _permit = semaphore.acquire().await?;
            
            match self.resolve_single_package(&package_name, &version_req).await {
                Ok(package_metadata) => {
                    let resolved_url = self.build_tarball_url(&package_name, &package_metadata.version)?;
                    
                    let mut dependencies = HashMap::new();
                    
                    // Add regular dependencies
                    for (dep_name, dep_version_req) in &package_metadata.dependencies {
                        if let Ok(dep_resolved) = self.resolve_single_package(dep_name, dep_version_req).await {
                            let dep_url = self.build_tarball_url(dep_name, &dep_resolved.version)?;
                            dependencies.insert(dep_name.clone(), ResolvedDependency {
                                name: dep_name.clone(),
                                version: dep_resolved.version.clone(),
                                resolved_url: dep_url,
                                integrity: dep_resolved.dist.integrity.unwrap_or_default(),
                                dependencies: HashMap::new(), // Will be filled recursively
                                dev: false,
                                optional: false,
                                peer: false,
                            });
                            
                            resolution_queue.push_back((dep_name.clone(), dep_version_req.clone(), false, false, false));
                        }
                    }

                    // Add dev dependencies if included
                    if context.include_dev {
                        for (dep_name, dep_version_req) in &package_metadata.dev_dependencies {
                            if let Ok(dep_resolved) = self.resolve_single_package(dep_name, dep_version_req).await {
                                let dep_url = self.build_tarball_url(dep_name, &dep_resolved.version)?;
                                dependencies.insert(dep_name.clone(), ResolvedDependency {
                                    name: dep_name.clone(),
                                    version: dep_resolved.version.clone(),
                                    resolved_url: dep_url,
                                    integrity: dep_resolved.dist.integrity.unwrap_or_default(),
                                    dependencies: HashMap::new(),
                                    dev: true,
                                    optional: false,
                                    peer: false,
                                });
                                
                                resolution_queue.push_back((dep_name.clone(), dep_version_req.clone(), true, false, false));
                            }
                        }
                    }

                    // Add optional dependencies if included
                    if context.include_optional {
                        for (dep_name, dep_version_req) in &package_metadata.optional_dependencies {
                            if let Ok(dep_resolved) = self.resolve_single_package(dep_name, dep_version_req).await {
                                let dep_url = self.build_tarball_url(dep_name, &dep_resolved.version)?;
                                dependencies.insert(dep_name.clone(), ResolvedDependency {
                                    name: dep_name.clone(),
                                    version: dep_resolved.version.clone(),
                                    resolved_url: dep_url,
                                    integrity: dep_resolved.dist.integrity.unwrap_or_default(),
                                    dependencies: HashMap::new(),
                                    dev: false,
                                    optional: true,
                                    peer: false,
                                });
                                
                                resolution_queue.push_back((dep_name.clone(), dep_version_req.clone(), false, true, false));
                            }
                        }
                    }

                    // Add peer dependencies if included
                    if context.include_peer {
                        for (dep_name, dep_version_req) in &package_metadata.peer_dependencies {
                            if let Ok(dep_resolved) = self.resolve_single_package(dep_name, dep_version_req).await {
                                let dep_url = self.build_tarball_url(dep_name, &dep_resolved.version)?;
                                dependencies.insert(dep_name.clone(), ResolvedDependency {
                                    name: dep_name.clone(),
                                    version: dep_resolved.version.clone(),
                                    resolved_url: dep_url,
                                    integrity: dep_resolved.dist.integrity.unwrap_or_default(),
                                    dependencies: HashMap::new(),
                                    dev: false,
                                    optional: false,
                                    peer: true,
                                });
                                
                                resolution_queue.push_back((dep_name.clone(), dep_version_req.clone(), false, false, true));
                            }
                        }
                    }

                    let resolved_dep = ResolvedDependency {
                        name: package_name.clone(),
                        version: package_metadata.version.clone(),
                        resolved_url,
                        integrity: package_metadata.dist.integrity.unwrap_or_default(),
                        dependencies,
                        dev: is_dev,
                        optional: is_optional,
                        peer: is_peer,
                    };

                    resolved.insert(package_name.clone(), resolved_dep);
                }
                Err(e) => {
                    if is_optional {
                        eprintln!("Warning: Optional dependency {} could not be resolved: {}", package_name, e);
                    } else {
                        return Err(e).with_context(|| format!("Failed to resolve {}", package_name));
                    }
                }
            }

            resolving.remove(&package_name);
        }

        // Resolve version conflicts
        self.resolve_conflicts(&mut resolved)?;

        Ok(resolved)
    }

    /// Resolve a single package to its metadata
    pub async fn resolve_single_package(&self, name: &str, version_req: &VersionReq) -> Result<PackageMetadata> {
        // Check cache first
        if let Some(cached) = self.cache_manager.get_metadata(name, &version_req.to_string()).await {
            return Ok(cached);
        }

        // Fetch from registry
        let registry_url = UrlUtils::build_registry_url(&self.registry_url, name)?;
        let registry_data: serde_json::Value = self.http_client.get_json(&registry_url).await
            .with_context(|| format!("Failed to fetch package metadata for {}", name))?;

        // Find the best matching version
        let versions = registry_data["versions"].as_object()
            .ok_or_else(|| NxError::PackageNotFound(name.to_string()))?;

        let mut matching_versions: Vec<Version> = versions
            .keys()
            .filter_map(|v| Version::parse(v).ok())
            .filter(|v| version_req.matches(v))
            .collect();

        if matching_versions.is_empty() {
            return Err(NxError::VersionNotFound(name.to_string(), version_req.to_string()).into());
        }

        // Sort by version (highest first)
        matching_versions.sort_by(|a, b| b.cmp(a));
        let selected_version = &matching_versions[0];

        // Get metadata for selected version
        let version_data = &versions[&selected_version.to_string()];
        let package_metadata: PackageMetadata = serde_json::from_value(version_data.clone())
            .with_context(|| format!("Failed to parse metadata for {}@{}", name, selected_version))?;

        // Cache the metadata
        self.cache_manager.cache_metadata(name, &selected_version.to_string(), package_metadata.clone()).await?;

        Ok(package_metadata)
    }

    /// Resolve version conflicts using semver compatibility
    fn resolve_conflicts(&self, resolved: &mut HashMap<String, ResolvedDependency>) -> Result<()> {
        let mut conflicts = HashMap::new();
        
        // Detect conflicts
        for (name, dep) in resolved.iter() {
            let key = &dep.name;
            if let Some(existing) = conflicts.get(key) {
                let existing_version: &Version = existing;
                if existing_version != &dep.version {
                    eprintln!(
                        "Warning: Version conflict for {}: {} vs {}",
                        key, existing_version, dep.version
                    );
                    
                    // Choose the higher version (simple strategy)
                    if dep.version > *existing_version {
                        conflicts.insert(key.clone(), dep.version.clone());
                    }
                }
            } else {
                conflicts.insert(key.clone(), dep.version.clone());
            }
        }

        // Apply conflict resolution
        for (name, resolved_version) in conflicts {
            if let Some(dep) = resolved.get_mut(&name) {
                if dep.version != resolved_version {
                    dep.version = resolved_version;
                    // Note: In a full implementation, we'd need to re-resolve dependencies
                    // for the new version, but this is a simplified version
                }
            }
        }

        Ok(())
    }

    /// Build tarball URL for package
    fn build_tarball_url(&self, name: &str, version: &Version) -> Result<String> {
        UrlUtils::build_tarball_url(&self.registry_url, name, &version.to_string())
    }

    /// Get latest version of a package
    pub async fn get_latest_version(&self, name: &str) -> Result<Version> {
        let registry_url = UrlUtils::build_registry_url(&self.registry_url, name)?;
        let registry_data: serde_json::Value = self.http_client.get_json(&registry_url).await
            .with_context(|| format!("Failed to fetch package metadata for {}", name))?;

        let dist_tags = registry_data["dist-tags"].as_object()
            .ok_or_else(|| NxError::PackageNotFound(name.to_string()))?;

        let latest_str = dist_tags["latest"].as_str()
            .ok_or_else(|| NxError::PackageNotFound(format!("{} (no latest tag)", name)))?;

        Version::parse(latest_str)
            .with_context(|| format!("Invalid version format: {}", latest_str))
    }

    /// Check if package exists
    pub async fn package_exists(&self, name: &str) -> bool {
        let registry_url = match UrlUtils::build_registry_url(&self.registry_url, name) {
            Ok(url) => url,
            Err(_) => return false,
        };

        match self.http_client.get_json::<serde_json::Value>(&registry_url).await {
            Ok(_) => true,
            Err(_) => false,
        }
    }
}
