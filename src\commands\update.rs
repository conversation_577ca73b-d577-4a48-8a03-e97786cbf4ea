use anyhow::{Context, Result};
use semver::{Version, VersionReq};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Instant;

use crate::cache::CacheManager;
use crate::cli::UpdateArgs;
use crate::installer::PackageInstaller;
use crate::lockfile::LockfileManager;
use crate::resolver::DependencyResolver;
use crate::types::{ResolutionContext, PackageJson};
use crate::ui::UI;
use crate::utils::FileUtils;

pub async fn execute(args: UpdateArgs, ui: &UI) -> Result<()> {
    let start_time = Instant::now();

    let current_dir = std::env::current_dir()
        .context("Failed to get current directory")?;

    let install_root = if args.global {
        FileUtils::get_global_path()?
    } else {
        current_dir.clone()
    };

    if args.outdated {
        return show_outdated(&install_root, ui).await;
    }

    // Initialize components
    let cache_manager = Arc::new(CacheManager::new()?);
    let registry_url = "https://registry.npmjs.org".to_string();
    let resolver = DependencyResolver::new(cache_manager.clone(), registry_url.clone());
    let installer = PackageInstaller::new(cache_manager);

    // Read package.json
    let package_json_path = current_dir.join("package.json");
    if !package_json_path.exists() && !args.global {
        ui.error("No package.json found in current directory");
        return Ok(());
    }

    let mut package_json = if !args.global {
        Some(FileUtils::read_package_json(&package_json_path).await?)
    } else {
        None
    };

    // Determine packages to update
    let packages_to_update = if args.packages.is_empty() {
        // Update all packages
        get_all_packages_to_update(&package_json, args.global).await?
    } else {
        // Update specific packages
        args.packages.iter().map(|name| (name.clone(), VersionReq::STAR)).collect()
    };

    if packages_to_update.is_empty() {
        ui.info("No packages to update");
        return Ok(());
    }

    ui.info(&format!("Updating {} packages...", packages_to_update.len()));

    let mut updated_packages = Vec::new();

    for (package_name, _) in &packages_to_update {
        let spinner = ui.create_spinner(&format!("Checking {}", package_name));

        // Get latest version
        match resolver.get_latest_version(package_name).await {
            Ok(latest_version) => {
                // Check current version
                let current_version = get_current_version(package_name, &install_root).await?;

                if let Some(current) = current_version {
                    if latest_version > current {
                        ui.finish_progress(&spinner, &format!("{}: {} → {}", package_name, current, latest_version));
                        updated_packages.push((package_name.clone(), VersionReq::parse(&format!("^{}", latest_version))?));
                    } else {
                        ui.finish_progress(&spinner, &format!("{} is already up to date", package_name));
                    }
                } else {
                    ui.finish_progress(&spinner, &format!("{} not currently installed", package_name));
                }
            }
            Err(e) => {
                ui.finish_progress_error(&spinner, &format!("Failed to check {}: {}", package_name, e));
            }
        }
    }

    if updated_packages.is_empty() {
        ui.success("All packages are already up to date!");
        return Ok(());
    }

    // Install updated packages
    let context = ResolutionContext {
        root_path: install_root.clone(),
        registry_url,
        include_dev: true,
        include_optional: true,
        include_peer: true,
        production_only: false,
    };

    let resolved_deps = resolver.resolve_dependencies(&updated_packages, &context).await
        .context("Failed to resolve updated dependencies")?;

    let install_infos = installer.install_packages(&resolved_deps, &install_root, ui).await
        .context("Failed to install updated packages")?;

    // Update package.json if not global
    if !args.global {
        if let Some(ref mut pkg_json) = package_json {
            update_package_json(pkg_json, &updated_packages);
            FileUtils::write_package_json(&package_json_path, pkg_json).await?;
        }
    }

    // Update lockfile if not global
    if !args.global {
        LockfileManager::write_lockfile(&current_dir, &resolved_deps).await
            .context("Failed to write lockfile")?;
    }

    // Show summary
    ui.print_summary_table(&install_infos);

    let total_time = start_time.elapsed();
    ui.success(&format!("Updated {} packages in {}", updated_packages.len(), ui.format_duration(total_time)));

    Ok(())
}

async fn show_outdated(install_root: &std::path::Path, ui: &UI) -> Result<()> {
    let node_modules = FileUtils::get_node_modules_path(install_root);

    if !node_modules.exists() {
        ui.info("No packages installed");
        return Ok(());
    }

    let cache_manager = Arc::new(CacheManager::new()?);
    let registry_url = "https://registry.npmjs.org".to_string();
    let resolver = DependencyResolver::new(cache_manager, registry_url);

    let mut outdated = HashMap::new();
    let mut entries = tokio::fs::read_dir(&node_modules).await?;

    ui.info("Checking for outdated packages...");

    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        if path.is_dir() {
            let name = entry.file_name();
            let name_str = name.to_string_lossy();

            if name_str.starts_with('.') {
                continue;
            }

            if let Some(current_version) = get_current_version(&name_str, install_root).await? {
                match resolver.get_latest_version(&name_str).await {
                    Ok(latest_version) => {
                        if latest_version > current_version {
                            outdated.insert(name_str.to_string(), (current_version.to_string(), latest_version.to_string()));
                        }
                    }
                    Err(_) => {
                        // Skip packages that can't be checked
                    }
                }
            }
        }
    }

    ui.print_outdated_table(&outdated);
    Ok(())
}

async fn get_all_packages_to_update(package_json: &Option<PackageJson>, global: bool) -> Result<Vec<(String, VersionReq)>> {
    if global {
        // For global packages, we'd need to read from global directory
        // For now, return empty
        return Ok(Vec::new());
    }

    let mut packages = Vec::new();

    if let Some(pkg_json) = package_json {
        if let Some(deps) = &pkg_json.dependencies {
            for (name, _) in deps {
                packages.push((name.clone(), VersionReq::STAR));
            }
        }

        if let Some(dev_deps) = &pkg_json.dev_dependencies {
            for (name, _) in dev_deps {
                packages.push((name.clone(), VersionReq::STAR));
            }
        }
    }

    Ok(packages)
}

async fn get_current_version(package_name: &str, install_root: &std::path::Path) -> Result<Option<Version>> {
    let node_modules = FileUtils::get_node_modules_path(install_root);
    let package_path = if package_name.starts_with('@') {
        let parts: Vec<&str> = package_name.splitn(2, '/').collect();
        node_modules.join(&parts[0]).join(&parts[1])
    } else {
        node_modules.join(package_name)
    };

    let package_json_path = package_path.join("package.json");
    if package_json_path.exists() {
        let package_json = FileUtils::read_package_json(&package_json_path).await?;
        Ok(package_json.version)
    } else {
        Ok(None)
    }
}

fn update_package_json(package_json: &mut PackageJson, updated_packages: &[(String, VersionReq)]) {
    for (name, version_req) in updated_packages {
        let version_str = version_req.to_string();

        // Update in dependencies if it exists there
        if let Some(ref mut deps) = package_json.dependencies {
            if deps.contains_key(name) {
                deps.insert(name.clone(), version_str.clone());
                continue;
            }
        }

        // Update in dev_dependencies if it exists there
        if let Some(ref mut dev_deps) = package_json.dev_dependencies {
            if dev_deps.contains_key(name) {
                dev_deps.insert(name.clone(), version_str);
            }
        }
    }
}
